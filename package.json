{"name": "game-guide-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "crawl": "tsx scripts/crawl.ts", "crawl:test": "tsx scripts/crawl.ts --test", "crawl:full": "tsx scripts/crawl.ts --full", "crawl:demo": "tsx scripts/demo-crawl.ts"}, "dependencies": {"@headlessui/react": "^2.2.9", "@types/cheerio": "^0.22.35", "cheerio": "^1.1.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.544.0", "next": "15.5.4", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tsx": "^4.20.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "tailwindcss": "^4", "typescript": "^5"}}