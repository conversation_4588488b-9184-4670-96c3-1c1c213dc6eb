import * as cheerio from 'cheerio'
import { ScrapedArticle, GameSpewArticle, ScraperConfig, ScrapingResult } from '../types'

export class GameSpewScraper {
  private config: ScraperConfig
  private baseUrl = 'https://www.gamespew.com'
  private userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
  ]

  constructor(config: Partial<ScraperConfig> = {}) {
    this.config = {
      baseUrl: 'https://www.gamespew.com',
      maxPages: 5,
      delayBetweenRequests: 8000,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
      respectRobots: false,
      timeout: 45000,
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Microsoft Edge";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1',
        'Connection': 'keep-alive'
      },
      randomDelayRange: [3000, 12000],
      ...config
    }
  }

  /**
   * 获取攻略列表页面的文章链接
   */
  async getGuideArticleUrls(maxPages: number = 3): Promise<string[]> {
    const urls: string[] = []
    
    try {
      for (let page = 1; page <= maxPages; page++) {
        const pageUrl = page === 1 
          ? `${this.baseUrl}/category/tips-tricks/`
          : `${this.baseUrl}/category/tips-tricks/page/${page}/`
        
        console.log(`正在爬取第 ${page} 页: ${pageUrl}`)
        
        // 添加随机延迟避免被检测
        if (page > 1) {
          const [minDelay, maxDelay] = this.config.randomDelayRange || [3000, 12000]
          const randomDelay = Math.random() * (maxDelay - minDelay) + minDelay
          console.log(`等待 ${Math.round(randomDelay/1000)}s 后继续...`)
          await this.delay(randomDelay)
        }

        const response = await fetch(pageUrl, {
          headers: {
            'User-Agent': this.config.userAgent,
            ...this.config.headers
          },
          signal: AbortSignal.timeout(this.config.timeout || 45000)
        })
        
        if (!response.ok) {
          console.error(`页面请求失败: ${response.status} ${response.statusText}`)
          console.error(`请求URL: ${pageUrl}`)
          console.error(`响应头:`, Object.fromEntries(response.headers.entries()))
          continue
        }
        
        const html = await response.text()
        const $ = cheerio.load(html)
        
        // 提取文章链接
        $('article h3 a, article h2 a').each((_, element) => {
          const href = $(element).attr('href')
          if (href && href.includes('/202')) { // 过滤出2025年的文章
            urls.push(href)
          }
        })
        
        // 延迟请求
        await this.delay(this.config.delayBetweenRequests)
      }
      
      console.log(`总共找到 ${urls.length} 篇攻略文章`)
      return [...new Set(urls)] // 去重
      
    } catch (error) {
      console.error('获取文章列表失败:', error)
      return []
    }
  }

  /**
   * 爬取单篇文章内容（带重试逻辑）
   */
  async scrapeArticle(url: string, maxRetries: number = 3): Promise<ScrapingResult> {
    let lastError: string = ''

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          console.log(`重试第 ${attempt} 次: ${url}`)
          // 重试前等待更长时间
          await this.delay(5000 + attempt * 2000)
        } else {
          console.log(`正在爬取文章: ${url}`)
        }

        // 在重试时使用不同的User-Agent
        const currentUserAgent = attempt > 0 ? this.userAgents[attempt % this.userAgents.length] : this.config.userAgent

        const response = await fetch(url, {
          headers: {
            'User-Agent': currentUserAgent,
            ...this.config.headers
          },
          signal: AbortSignal.timeout(this.config.timeout || 45000)
        })

        if (!response.ok) {
          lastError = `HTTP ${response.status}: ${response.statusText}`
          console.error(`请求失败 (尝试 ${attempt + 1}/${maxRetries + 1}): ${lastError}`)

          // 如果是403错误，尝试更换User-Agent
          if (response.status === 403 && attempt < maxRetries) {
            console.log('检测到403错误，尝试更换User-Agent...')
            continue
          }

          // 如果是最后一次尝试，返回错误
          if (attempt === maxRetries) {
            return {
              success: false,
              error: lastError,
              retryCount: attempt
            }
          }
          continue
        }

        // 成功获取响应，处理内容
        const html = await response.text()
        const $ = cheerio.load(html)

        // 提取文章信息
        const title = $('h1.entry-title, h1').first().text().trim()
        const author = $('.author-name, .by a, [rel="author"]').first().text().trim() || 'GameSpew Team'

        // 提取发布日期
        const dateText = $('.published, .entry-date, time').first().attr('datetime') ||
                        $('.published, .entry-date, time').first().text().trim()
        const publishedAt = this.parseDate(dateText)

        // 提取文章内容
        const contentElement = $('.entry-content, .post-content, article .content').first()

        // 清理内容 - 移除广告、分享按钮等
        contentElement.find('.ad, .advertisement, .share, .social, script, style').remove()

        // 确保图片有完整的URL
        contentElement.find('img').each((_, img) => {
          const $img = $(img)
          const src = $img.attr('src')
          if (src && !src.startsWith('http')) {
            $img.attr('src', this.resolveUrl(src))
          }
        })

        const content = contentElement.html() || ''
        const textContent = contentElement.text().trim()

        // 生成摘要（前200字符）
        const excerpt = textContent.substring(0, 200) + (textContent.length > 200 ? '...' : '')

        // 提取特色图片
        const imageUrl = $('meta[property="og:image"]').attr('content') ||
                        $('.featured-image img, .entry-image img').first().attr('src') ||
                        $('img').first().attr('src')

        // 提取标签
        const tags: string[] = []
        $('.tag, .tags a, .post-tags a').each((_, element) => {
          const tag = $(element).text().trim()
          if (tag) tags.push(tag)
        })

        // 判断难度（基于内容关键词）
        const difficulty = this.determineDifficulty(textContent)

        const article: ScrapedArticle = {
          title,
          content,
          excerpt,
          author,
          publishedAt,
          imageUrl: imageUrl ? this.resolveUrl(imageUrl) : undefined,
          originalUrl: url,
          category: 'tips-tricks',
          tags,
          difficulty
        }

        return {
          success: true,
          article,
          retryCount: attempt
        }

      } catch (error) {
        lastError = error instanceof Error ? error.message : '未知错误'
        console.error(`爬取失败 (尝试 ${attempt + 1}/${maxRetries + 1}): ${lastError}`)

        if (attempt === maxRetries) {
          break
        }
      }
    }

    // 所有重试都失败了
    return {
      success: false,
      error: lastError,
      retryCount: maxRetries
    }
  }

  /**
   * 批量爬取文章
   */
  async scrapeMultipleArticles(urls: string[]): Promise<ScrapingResult[]> {
    const results: ScrapingResult[] = []
    
    for (let i = 0; i < urls.length; i++) {
      const url = urls[i]
      console.log(`进度: ${i + 1}/${urls.length}`)
      
      const result = await this.scrapeArticle(url)
      results.push(result)
      
      // 延迟请求避免被封
      if (i < urls.length - 1) {
        await this.delay(this.config.delayBetweenRequests)
      }
    }
    
    return results
  }

  /**
   * 解析日期字符串
   */
  private parseDate(dateStr: string): string {
    if (!dateStr) return new Date().toISOString()
    
    try {
      // 尝试解析各种日期格式
      const date = new Date(dateStr)
      return date.toISOString()
    } catch {
      return new Date().toISOString()
    }
  }

  /**
   * 根据内容判断难度
   */
  private determineDifficulty(content: string): 'beginner' | 'intermediate' | 'advanced' {
    const lowerContent = content.toLowerCase()
    
    const beginnerKeywords = ['beginner', 'start', 'basic', 'simple', 'easy', 'guide', 'how to']
    const advancedKeywords = ['advanced', 'expert', 'complex', 'difficult', 'master', 'pro']
    
    const beginnerCount = beginnerKeywords.reduce((count, keyword) => 
      count + (lowerContent.split(keyword).length - 1), 0)
    const advancedCount = advancedKeywords.reduce((count, keyword) => 
      count + (lowerContent.split(keyword).length - 1), 0)
    
    if (advancedCount > beginnerCount) return 'advanced'
    if (beginnerCount > 0) return 'beginner'
    return 'intermediate'
  }

  /**
   * 解析相对URL为绝对URL
   */
  private resolveUrl(url: string): string {
    if (url.startsWith('http')) return url
    if (url.startsWith('//')) return 'https:' + url
    if (url.startsWith('/')) return this.baseUrl + url
    return url
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 检查robots.txt（简化版本）
   */
  async checkRobots(): Promise<boolean> {
    if (!this.config.respectRobots) return true
    
    try {
      const response = await fetch(`${this.baseUrl}/robots.txt`)
      const robotsText = await response.text()
      
      // 简单检查是否禁止爬取
      return !robotsText.toLowerCase().includes('disallow: /')
    } catch {
      return true // 如果无法获取robots.txt，默认允许
    }
  }
}
