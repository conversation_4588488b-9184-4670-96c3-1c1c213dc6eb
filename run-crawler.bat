@echo off
title Crawler Runner

echo.
echo ========================================
echo Game Guide Crawler System
echo ========================================
echo.

:: Check if Node.js is installed
echo Checking Node.js environment...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js not detected
    echo Please install Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js environment check passed
echo.

:: Enter crawler system directory
echo Entering crawler system directory...
cd /d "%~dp0crawler-system"

if not exist "package.json" (
    echo Error: Cannot find crawler system files
    echo Please ensure you are running this script in the correct project directory
    echo.
    pause
    exit /b 1
)

:: Check if dependencies are installed
echo Checking dependency installation status...
if not exist "node_modules" (
    echo First run, installing dependencies...
    echo This may take a few minutes, please wait...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo Dependency installation failed
        echo.
        pause
        exit /b 1
    )
    echo Dependency installation completed
    echo.
) else (
    echo Dependencies already installed
    echo.
)

:: Display crawler mode selection menu
echo Please select crawler mode:
echo.
echo [1] Test mode (crawl 1 article)
echo [2] Default mode (crawl 5 articles)
echo [3] Full mode (crawl 20 articles)
echo [4] Demo mode (quick demo)
echo.
set /p choice="Please enter your choice (1-4): "

if "%choice%"=="1" (
    echo.
    echo Starting test mode...
    npm run crawl:test
) else if "%choice%"=="2" (
    echo.
    echo Starting default mode...
    npm run crawl
) else if "%choice%"=="3" (
    echo.
    echo Starting full mode...
    npm run crawl:full
) else if "%choice%"=="4" (
    echo.
    echo Starting demo mode...
    npm run crawl:demo
) else (
    echo.
    echo Invalid choice, using test mode by default
    echo.
    npm run crawl:test
)

echo.
echo Crawler task completed!
echo Tip: You can start the website to view newly added content
echo    Double-click "start-website.bat" to start the website
echo.
pause
